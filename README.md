# Promptini - VS Code Extension for OpenRouter AI

## Project Overview

**Promptini** is a powerful VS Code extension that provides a seamless interface to interact with advanced AI models via the OpenRouter API. It brings the power of the Nvidia Llama 3.1 Nemotron Ultra 253B model directly into your development environment, enabling developers to chat with AI, get code assistance, and even have the AI execute commands automatically.

## 🚀 Key Features

### 1. **Chat Mode**
- **Natural Conversation**: Have interactive conversations with the AI assistant directly in VS Code
- **Code Assistance**: Get help with coding, debugging, and problem-solving
- **Documentation Generation**: Generate documentation for your code
- **Learning Support**: Learn new programming concepts and techniques
- **Conversation History**: Persistent conversation tracking across sessions
- **Markdown Support**: Rich formatting for code snippets and explanations
- **Copy/Download**: Easy copying of responses to clipboard

### 2. **Agent Mode** (Planned/Enhanced)
- **Command Execution**: AI can suggest and execute terminal commands with user confirmation
- **File Modification**: AI can create, modify, and manage files in your workspace
- **Safety Checks**: Built-in safety mechanisms for potentially dangerous commands
- **Command History**: Track all executed commands for reference

### 3. **Agent Auto Mode** (Planned/Enhanced)
- **Fully Automated**: AI executes commands and modifies files without manual confirmation
- **Test Execution**: Automatically runs tests for created files
- **Terminal Monitoring**: Monitors terminal commands until completion
- **Auto-Response**: Automatically handles terminal prompts (Y/N questions)
- **Command Extraction**: Intelligent extraction of commands from AI responses

### 4. **Advanced Features** (Planned)
- **Context Column**: Dedicated interface column for tracking and maintaining context
- **Persistent Task Tracking**: `.temporary` folder system for ongoing tasks
- **Self-Verification**: Built-in capabilities to verify and validate changes
- **Web Access**: Dependency checking and web-based research capabilities
- **Checkpoints**: Rollback system for undoing changes
- **Glassmorphism UI**: Modern interface with subtle cyberpunk aesthetic hints

## 🏗️ Architecture

### Core Components

1. **VS Code Extension** (`extension.js`)
   - Main entry point and VS Code integration
   - WebView panel management
   - Command registration and handling
   - API key management and storage

2. **WebView UI** (`media/`)
   - **HTML Interface** (`webview.html`): Clean, responsive user interface
   - **JavaScript Logic** (`main.js`): Client-side functionality and state management
   - **CSS Styling** (`styles.css`): Modern styling with glassmorphism elements

3. **Python Bridge** (`simple_openrouter.py`)
   - OpenRouter API communication
   - Request/response handling
   - Command extraction for Agent modes
   - Error handling and logging

4. **API Integration**
   - Direct integration with OpenRouter API
   - Support for multiple AI models (default: Nvidia Llama 3.1 Nemotron Ultra 253B)
   - Configurable parameters (max tokens, model selection)

### Data Flow

```
User Input → WebView UI → VS Code Extension → OpenRouter API → AI Model
                ↓                                    ↓
         State Management ← Response Processing ← API Response
```

## 🛠️ Technical Specifications

### Dependencies
- **Runtime**: Node.js with VS Code Extension API
- **HTTP Client**: Axios for API requests
- **Utilities**: UUID for unique identifiers, Marked for markdown processing
- **Development**: ESLint, TypeScript definitions, VS Code test framework

### Configuration
- **Default Model**: `nvidia/llama-3.1-nemotron-ultra-253b-v1:free`
- **Max Tokens**: 2000 (configurable)
- **Keyboard Shortcut**: `Ctrl+Shift+L` (Windows/Linux), `Cmd+Shift+L` (Mac)
- **API Key Storage**: Secure local storage in `secrets/` directory

### Security Features
- **API Key Protection**: Keys stored locally, never hardcoded
- **Content Security Policy**: Strict CSP for WebView security
- **Input Validation**: Comprehensive validation before API calls
- **Command Safety**: Confirmation required for potentially dangerous operations

## 📦 Installation & Setup

### Prerequisites
- VS Code 1.60.0 or higher
- OpenRouter API key (get one at [openrouter.ai](https://openrouter.ai/))

### Installation Methods

#### Method 1: VS Code Marketplace (Future)
1. Open VS Code Extensions panel (`Ctrl+Shift+X`)
2. Search for "Promptini"
3. Click "Install"

#### Method 2: From Source
```bash
# Clone the repository
git clone https://github.com/yourusername/promptini.git
cd promptini

# Install dependencies
npm install

# Build the extension
npm run build

# Package (optional)
npm run package
```

#### Method 3: Development Mode
1. Clone repository and open in VS Code
2. Press `F5` to launch extension development host
3. New VS Code window opens with extension loaded

### First-Time Setup
1. Launch Promptini (`Ctrl+Shift+L` or Command Palette → "Open Promptini")
2. Enter your OpenRouter API key when prompted
3. Key is automatically saved for future sessions

## 🎯 Usage

### Basic Chat
1. Open Promptini interface
2. Type your question or request
3. Press Enter or click Send
4. View AI response with syntax highlighting and formatting

### Agent Mode (When Available)
1. Switch to Agent mode in the interface
2. Request specific actions (e.g., "Create a new React component")
3. Review suggested commands
4. Confirm execution

### Agent Auto Mode (When Available)
1. Enable Auto mode
2. Provide high-level instructions
3. AI automatically executes necessary commands
4. Monitor progress in debug logs

## 🔧 Development

### Project Structure
```
promptini/
├── extension.js              # Main extension entry point
├── package.json             # Extension manifest and dependencies
├── media/                   # WebView UI assets
│   ├── webview.html        # Main interface HTML
│   ├── main.js             # Client-side JavaScript
│   └── styles.css          # UI styling
├── simple_openrouter.py    # Python API bridge
├── docs/                   # Comprehensive documentation
├── .vscode/                # VS Code configuration
│   ├── launch.json         # Debug configurations
│   └── tasks.json          # Build tasks
└── secrets/                # Secure API key storage
```

### Build Scripts
- `npm run build`: Package extension for distribution
- `npm run compile`: Compile TypeScript (placeholder for JS project)
- `npm run lint`: Run ESLint code quality checks
- `npm run test`: Execute test suite

### Debugging
1. Open project in VS Code
2. Set breakpoints in code
3. Press `F5` to launch debug session
4. Use Debug Console for inspection

## 🔮 Future Enhancements

### Planned Features
- **Multi-Mode Interface**: Seamless switching between Chat, Agent, and Auto modes
- **Enhanced Context Management**: Persistent context tracking across sessions
- **Advanced File Operations**: Comprehensive file creation, modification, and management
- **Intelligent Testing**: Automatic test generation and execution
- **Web Integration**: Real-time dependency checking and documentation lookup
- **Checkpoint System**: Version control-like rollback capabilities
- **Improved UI**: Enhanced glassmorphism design with cyberpunk elements

### Roadmap
1. **Phase 1**: Complete Agent and Auto mode implementation
2. **Phase 2**: Add context management and persistent task tracking
3. **Phase 3**: Implement web access and dependency checking
4. **Phase 4**: Enhanced UI and checkpoint system
5. **Phase 5**: Marketplace publication and community features

## 🤝 Contributing

### Getting Started
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

### Development Guidelines
- Follow existing code style and patterns
- Add comprehensive documentation for new features
- Ensure all tests pass before submitting
- Update relevant documentation files

## 📄 License

This project is open source. Please refer to the LICENSE file for specific terms and conditions.

## 🔗 Links

- **OpenRouter API**: [openrouter.ai](https://openrouter.ai/)
- **VS Code Extension API**: [code.visualstudio.com/api](https://code.visualstudio.com/api)
- **Documentation**: See `docs/` directory for detailed guides

---

**Promptini** represents the future of AI-assisted development, bringing powerful language models directly into your coding workflow with an intuitive, secure, and feature-rich interface.
