# Promptini - VS Code Extension for Local AI Code Generation

## Project Overview

**Promptini** is a powerful VS Code extension that provides a seamless interface to interact with local AI models for code generation and assistance. It leverages the CodeGen-350M-mono model running locally on your machine, enabling developers to chat with AI, get code assistance, and even have the AI execute commands automatically - all without requiring internet connectivity or external API calls.

## 🚀 Key Features

### 1. **Chat Mode**
- **Natural Conversation**: Have interactive conversations with the local AI assistant directly in VS Code
- **Code Generation**: Generate code snippets, functions, and complete files using CodeGen-350M-mono
- **Code Completion**: Intelligent code completion and suggestions
- **Code Assistance**: Get help with coding, debugging, and problem-solving
- **Documentation Generation**: Generate documentation for your code
- **Learning Support**: Learn new programming concepts and techniques
- **Conversation History**: Persistent conversation tracking across sessions
- **Markdown Support**: Rich formatting for code snippets and explanations
- **Copy/Download**: Easy copying of responses to clipboard
- **Offline Operation**: Works completely offline with local model inference

### 2. **Agent Mode** (Planned/Enhanced)
- **Command Execution**: AI can suggest and execute terminal commands with user confirmation
- **File Modification**: AI can create, modify, and manage files in your workspace
- **Safety Checks**: Built-in safety mechanisms for potentially dangerous commands
- **Command History**: Track all executed commands for reference

### 3. **Agent Auto Mode** (Planned/Enhanced)
- **Fully Automated**: AI executes commands and modifies files without manual confirmation
- **Test Execution**: Automatically runs tests for created files
- **Terminal Monitoring**: Monitors terminal commands until completion
- **Auto-Response**: Automatically handles terminal prompts (Y/N questions)
- **Command Extraction**: Intelligent extraction of commands from AI responses

### 4. **Advanced Features** (Planned)
- **Context Column**: Dedicated interface column for tracking and maintaining context
- **Persistent Task Tracking**: `.temporary` folder system for ongoing tasks
- **Self-Verification**: Built-in capabilities to verify and validate changes
- **Local Model Management**: Easy switching between different local models
- **Model Performance Monitoring**: Track inference speed and resource usage
- **Checkpoints**: Rollback system for undoing changes
- **Glassmorphism UI**: Modern interface with subtle cyberpunk aesthetic hints
- **Privacy-First**: All processing happens locally, ensuring code privacy

## 🏗️ Architecture

### Core Components

1. **VS Code Extension** (`extension.js`)
   - Main entry point and VS Code integration
   - WebView panel management
   - Command registration and handling
   - Local model configuration and management

2. **WebView UI** (`media/`)
   - **HTML Interface** (`webview.html`): Clean, responsive user interface
   - **JavaScript Logic** (`main.js`): Client-side functionality and state management
   - **CSS Styling** (`styles.css`): Modern styling with glassmorphism elements

3. **Local Model Bridge** (`local_model_bridge.py`)
   - Local CodeGen-350M-mono model loading and inference
   - Request/response handling for local model
   - Command extraction for Agent modes
   - Error handling and logging
   - Model performance monitoring

4. **Local Model Integration**
   - Direct integration with CodeGen-350M-mono model in `models/` directory
   - Hugging Face Transformers library for model loading
   - Configurable parameters (max tokens, temperature, top-p)
   - GPU acceleration support when available

### Data Flow

```
User Input → WebView UI → VS Code Extension → Local Model Bridge → CodeGen-350M-mono
                ↓                                    ↓                      ↓
         State Management ← Response Processing ← Model Inference ← Local Processing
```

## 🛠️ Technical Specifications

### Dependencies
- **Runtime**: Node.js with VS Code Extension API
- **Python Environment**: Python 3.8+ with PyTorch and Transformers
- **ML Libraries**: Hugging Face Transformers, PyTorch, tokenizers
- **Utilities**: UUID for unique identifiers, Marked for markdown processing
- **Development**: ESLint, TypeScript definitions, VS Code test framework

### Configuration
- **Default Model**: `CodeGen-350M-mono` (located in `models/codegen-350M-mono/`)
- **Max Tokens**: 512 (configurable, optimized for CodeGen model)
- **Temperature**: 0.7 (configurable for creativity vs consistency)
- **Keyboard Shortcut**: `Ctrl+Shift+L` (Windows/Linux), `Cmd+Shift+L` (Mac)
- **Model Storage**: Local model files in `models/` directory

### Security Features
- **Privacy-First**: All processing happens locally, no data leaves your machine
- **Content Security Policy**: Strict CSP for WebView security
- **Input Validation**: Comprehensive validation before model inference
- **Command Safety**: Confirmation required for potentially dangerous operations
- **Local Storage**: All conversations and data stored locally

## 📦 Installation & Setup

### Prerequisites
- VS Code 1.60.0 or higher
- Python 3.8 or higher
- PyTorch (CPU or GPU version)
- Hugging Face Transformers library
- At least 4GB RAM (8GB recommended for better performance)
- CodeGen-350M-mono model files (included in `models/` directory)

### Installation Methods

#### Method 1: VS Code Marketplace (Future)
1. Open VS Code Extensions panel (`Ctrl+Shift+X`)
2. Search for "Promptini"
3. Click "Install"

#### Method 2: From Source
```bash
# Clone the repository
git clone https://github.com/yourusername/promptini.git
cd promptini

# Install Node.js dependencies
npm install

# Install Python dependencies
pip install torch transformers tokenizers

# Build the extension
npm run build

# Package (optional)
npm run package
```

#### Method 3: Development Mode
1. Clone repository and open in VS Code
2. Press `F5` to launch extension development host
3. New VS Code window opens with extension loaded

### First-Time Setup
1. Ensure Python dependencies are installed: `pip install torch transformers tokenizers`
2. Verify the CodeGen-350M-mono model is present in `models/codegen-350M-mono/`
3. Launch Promptini (`Ctrl+Shift+L` or Command Palette → "Open Promptini")
4. The extension will automatically load the local model on first use
5. No API keys or internet connection required!

## 🎯 Usage

### Basic Chat
1. Open Promptini interface
2. Type your code generation request or question
3. Press Enter or click Send
4. View AI-generated code with syntax highlighting and formatting
5. All processing happens locally on your machine

### Agent Mode (When Available)
1. Switch to Agent mode in the interface
2. Request specific actions (e.g., "Create a new React component")
3. Review suggested commands
4. Confirm execution

### Agent Auto Mode (When Available)
1. Enable Auto mode
2. Provide high-level instructions
3. AI automatically executes necessary commands
4. Monitor progress in debug logs

## 🔧 Development

### Project Structure
```
promptini/
├── extension.js              # Main extension entry point
├── package.json             # Extension manifest and dependencies
├── media/                   # WebView UI assets
│   ├── webview.html        # Main interface HTML
│   ├── main.js             # Client-side JavaScript
│   └── styles.css          # UI styling
├── local_model_bridge.py   # Python local model bridge
├── models/                 # Local AI models
│   └── codegen-350M-mono/  # CodeGen model files
├── docs/                   # Comprehensive documentation
├── .vscode/                # VS Code configuration
│   ├── launch.json         # Debug configurations
│   └── tasks.json          # Build tasks
└── config/                 # Local configuration storage
```

### Build Scripts
- `npm run build`: Package extension for distribution
- `npm run compile`: Compile TypeScript (placeholder for JS project)
- `npm run lint`: Run ESLint code quality checks
- `npm run test`: Execute test suite

### Debugging
1. Open project in VS Code
2. Set breakpoints in code
3. Press `F5` to launch debug session
4. Use Debug Console for inspection

## 🔮 Future Enhancements

### Planned Features
- **Multi-Mode Interface**: Seamless switching between Chat, Agent, and Auto modes
- **Enhanced Context Management**: Persistent context tracking across sessions
- **Advanced File Operations**: Comprehensive file creation, modification, and management
- **Intelligent Testing**: Automatic test generation and execution
- **Model Management**: Easy switching between different local models
- **Performance Optimization**: GPU acceleration and model quantization
- **Checkpoint System**: Version control-like rollback capabilities
- **Improved UI**: Enhanced glassmorphism design with cyberpunk elements
- **Code Completion**: Real-time code completion as you type

### Roadmap
1. **Phase 1**: Complete Agent and Auto mode implementation
2. **Phase 2**: Add context management and persistent task tracking
3. **Phase 3**: Implement model management and performance optimization
4. **Phase 4**: Enhanced UI and checkpoint system
5. **Phase 5**: Marketplace publication and community features

## 🤝 Contributing

### Getting Started
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

### Development Guidelines
- Follow existing code style and patterns
- Add comprehensive documentation for new features
- Ensure all tests pass before submitting
- Update relevant documentation files

## 📄 License

This project is open source. Please refer to the LICENSE file for specific terms and conditions.

## 🔗 Links

- **CodeGen Model**: [Salesforce CodeGen on Hugging Face](https://huggingface.co/Salesforce/codegen-350M-mono)
- **VS Code Extension API**: [code.visualstudio.com/api](https://code.visualstudio.com/api)
- **Hugging Face Transformers**: [huggingface.co/transformers](https://huggingface.co/transformers)
- **PyTorch**: [pytorch.org](https://pytorch.org/)
- **Documentation**: See `docs/` directory for detailed guides

---

**Promptini** represents the future of privacy-first AI-assisted development, bringing powerful local language models directly into your coding workflow with an intuitive, secure, and feature-rich interface that keeps your code completely private.
